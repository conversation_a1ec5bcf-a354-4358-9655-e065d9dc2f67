# 中英文单词消消乐

一个有趣的中英文单词配对消除游戏，帮助学习和记忆英语单词。

## 功能特点

- 🎮 经典消消乐玩法
- 🇨🇳🇺🇸 中英文单词配对
- 📚 可自定义单词本
- 🎯 计分系统
- 🔄 可重复游戏

## 游戏规则

1. 游戏开始时，网格中会随机分布中文和英文单词卡片
2. 点击两张卡片，如果它们是匹配的中英文单词对，就会消除
3. 红色文字表示中文单词，蓝色文字表示英文单词
4. 消除所有卡片即可获胜
5. 每成功匹配一对单词获得10分

## 操作方法

- **鼠标左键**: 选择/取消选择卡片
- **R键**: 游戏结束后重新开始
- **关闭窗口**: 退出游戏

## 安装和运行

### 环境要求

- Python 3.6+
- pygame库

### 安装依赖

```bash
pip install pygame
```

### 运行游戏

```bash
python main.py
```

或者直接运行游戏文件：

```bash
python word_match_game.py
```

## 自定义单词本

游戏会自动加载 `words.json` 文件中的单词。你可以编辑这个文件来添加自己的单词：

```json
[
    {"chinese": "苹果", "english": "apple"},
    {"chinese": "香蕉", "english": "banana"},
    ...
]
```

### 单词本格式说明

- 文件格式：JSON
- 编码：UTF-8
- 结构：包含中英文对照的对象数组
- 字段：
  - `chinese`: 中文单词
  - `english`: 对应的英文单词

## 游戏配置

你可以在 `word_match_game.py` 文件中修改以下配置：

- `GRID_SIZE`: 网格大小（默认6x6）
- `CELL_SIZE`: 卡片大小（默认80像素）
- `COLORS`: 颜色配置
- `WINDOW_WIDTH/HEIGHT`: 窗口大小

## 文件结构

```
消消乐/
├── main.py              # 游戏启动脚本
├── word_match_game.py   # 游戏主程序
├── words.json           # 单词本文件
└── README.md           # 说明文档
```

## 游戏截图说明

游戏界面包含：
- 标题栏：显示游戏名称
- 分数栏：显示当前分数和匹配进度
- 游戏网格：显示所有单词卡片
- 状态提示：游戏结束时的提示信息

## 技术特点

- 使用pygame库开发，界面简洁美观
- 面向对象设计，代码结构清晰
- 支持中文显示，字体渲染优化
- 随机生成游戏布局，增加可玩性
- 错误处理完善，用户体验良好

## 扩展建议

可以考虑添加以下功能：
- 难度等级选择
- 时间限制模式
- 音效和背景音乐
- 成就系统
- 在线单词本更新
- 多人对战模式

## 故障排除

### 常见问题

1. **ImportError: No module named 'pygame'**
   - 解决方案：运行 `pip install pygame` 安装pygame库

2. **中文显示乱码**
   - 确保系统支持UTF-8编码
   - 检查words.json文件编码是否为UTF-8

3. **游戏运行缓慢**
   - 检查系统性能
   - 可以调小GRID_SIZE减少卡片数量

4. **单词本加载失败**
   - 检查words.json文件格式是否正确
   - 确保文件存在于游戏目录中

## 许可证

本项目仅供学习和娱乐使用。

## 贡献

欢迎提交问题报告和改进建议！
