import pygame
import random
import sys
from typing import List, Tuple, Set

# 游戏常量
GRID_SIZE = 8
CELL_SIZE = 60
WINDOW_WIDTH = GRID_SIZE * CELL_SIZE + 200
WINDOW_HEIGHT = GRID_SIZE * CELL_SIZE + 100
FPS = 60

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 100, 200)
GREEN = (0, 200, 0)
RED = (200, 0, 0)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
LIGHT_BLUE = (173, 216, 230)

class WordPuzzleGame:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("单词消消乐")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)

        # 游戏状态
        self.grid = []
        self.selected_cells = []
        self.current_word = ""
        self.score = 0
        self.valid_words = self.load_word_list()

        # 初始化网格
        self.init_grid()

    def load_word_list(self) -> Set[str]:
        """加载有效单词列表"""
        # 这里使用一些常见的英文单词作为示例
        words = {
            "CAT", "DOG", "BIRD", "FISH", "TREE", "BOOK", "GAME", "PLAY",
            "WORD", "CODE", "LOVE", "LIFE", "TIME", "WORK", "HOME", "FOOD",
            "WATER", "LIGHT", "MUSIC", "HAPPY", "SMILE", "DREAM", "PEACE",
            "WORLD", "HEART", "MAGIC", "POWER", "SPACE", "OCEAN", "MOUNTAIN"
        }
        return words

    def init_grid(self):
        """初始化游戏网格"""
        # 常见字母的权重分布
        letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        weights = [8, 2, 3, 4, 12, 2, 3, 6, 7, 1, 1, 4, 2, 7, 8, 2, 1, 6, 6, 9, 3, 2, 2, 1, 2, 1]

        self.grid = []
        for i in range(GRID_SIZE):
            row = []
            for j in range(GRID_SIZE):
                letter = random.choices(letters, weights=weights)[0]
                row.append(letter)
            self.grid.append(row)

    def get_cell_pos(self, row: int, col: int) -> Tuple[int, int]:
        """获取网格单元格的屏幕坐标"""
        x = col * CELL_SIZE + 10
        y = row * CELL_SIZE + 10
        return x, y

    def get_grid_pos(self, mouse_x: int, mouse_y: int) -> Tuple[int, int]:
        """根据鼠标坐标获取网格位置"""
        col = (mouse_x - 10) // CELL_SIZE
        row = (mouse_y - 10) // CELL_SIZE
        if 0 <= row < GRID_SIZE and 0 <= col < GRID_SIZE:
            return row, col
        return -1, -1

    def is_adjacent(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> bool:
        """检查两个位置是否相邻（包括对角线）"""
        row1, col1 = pos1
        row2, col2 = pos2
        return abs(row1 - row2) <= 1 and abs(col1 - col2) <= 1 and pos1 != pos2

    def can_add_cell(self, row: int, col: int) -> bool:
        """检查是否可以添加这个单元格到当前选择"""
        if not self.selected_cells:
            return True

        # 检查是否已经选择过这个单元格
        if (row, col) in self.selected_cells:
            return False

        # 检查是否与最后选择的单元格相邻
        last_pos = self.selected_cells[-1]
        return self.is_adjacent(last_pos, (row, col))

    def add_cell_to_selection(self, row: int, col: int):
        """添加单元格到选择列表"""
        if self.can_add_cell(row, col):
            self.selected_cells.append((row, col))
            self.current_word += self.grid[row][col]

    def clear_selection(self):
        """清空当前选择"""
        self.selected_cells = []
        self.current_word = ""

    def is_valid_word(self, word: str) -> bool:
        """检查单词是否有效"""
        return len(word) >= 3 and word.upper() in self.valid_words

    def remove_selected_letters(self):
        """移除选中的字母并让上方字母下落"""
        if not self.selected_cells:
            return

        # 按列分组选中的单元格
        columns = {}
        for row, col in self.selected_cells:
            if col not in columns:
                columns[col] = []
            columns[col].append(row)

        # 对每列进行处理
        for col, rows in columns.items():
            rows.sort(reverse=True)  # 从下往上处理

            # 移除选中的字母
            for row in rows:
                # 将上方的字母下移
                for r in range(row, 0, -1):
                    self.grid[r][col] = self.grid[r-1][col]

                # 在顶部生成新字母
                letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                weights = [8, 2, 3, 4, 12, 2, 3, 6, 7, 1, 1, 4, 2, 7, 8, 2, 1, 6, 6, 9, 3, 2, 2, 1, 2, 1]
                self.grid[0][col] = random.choices(letters, weights=weights)[0]

    def submit_word(self):
        """提交当前单词"""
        if self.is_valid_word(self.current_word):
            # 计算分数：基础分数 + 长度奖励
            word_score = len(self.current_word) * 10 + (len(self.current_word) - 3) * 5
            self.score += word_score

            # 移除选中的字母
            self.remove_selected_letters()

            print(f"有效单词: {self.current_word} (+{word_score}分)")
        else:
            print(f"无效单词: {self.current_word}")

        self.clear_selection()

    def draw_grid(self):
        """绘制游戏网格"""
        for row in range(GRID_SIZE):
            for col in range(GRID_SIZE):
                x, y = self.get_cell_pos(row, col)

                # 绘制单元格背景
                if (row, col) in self.selected_cells:
                    color = LIGHT_BLUE
                else:
                    color = WHITE

                pygame.draw.rect(self.screen, color, (x, y, CELL_SIZE, CELL_SIZE))
                pygame.draw.rect(self.screen, BLACK, (x, y, CELL_SIZE, CELL_SIZE), 2)

                # 绘制字母
                letter = self.grid[row][col]
                text = self.font.render(letter, True, BLACK)
                text_rect = text.get_rect(center=(x + CELL_SIZE//2, y + CELL_SIZE//2))
                self.screen.blit(text, text_rect)

    def draw_selection_lines(self):
        """绘制选择路径的连线"""
        if len(self.selected_cells) > 1:
            points = []
            for row, col in self.selected_cells:
                x, y = self.get_cell_pos(row, col)
                center_x = x + CELL_SIZE // 2
                center_y = y + CELL_SIZE // 2
                points.append((center_x, center_y))

            if len(points) > 1:
                pygame.draw.lines(self.screen, RED, False, points, 3)

    def draw_ui(self):
        """绘制用户界面"""
        # 绘制分数
        score_text = self.font.render(f"分数: {self.score}", True, BLACK)
        self.screen.blit(score_text, (GRID_SIZE * CELL_SIZE + 20, 20))

        # 绘制当前单词
        word_text = self.font.render(f"当前单词: {self.current_word}", True, BLACK)
        self.screen.blit(word_text, (GRID_SIZE * CELL_SIZE + 20, 60))

        # 绘制说明
        instructions = [
            "游戏说明:",
            "1. 点击并拖拽选择相邻字母",
            "2. 组成3个字母以上的单词",
            "3. 松开鼠标提交单词",
            "4. 有效单词会被消除并得分",
            "",
            "按 R 键重新开始游戏",
            "按 ESC 键退出游戏"
        ]

        y_offset = 120
        for instruction in instructions:
            text = self.small_font.render(instruction, True, BLACK)
            self.screen.blit(text, (GRID_SIZE * CELL_SIZE + 20, y_offset))
            y_offset += 25

    def handle_mouse_down(self, pos):
        """处理鼠标按下事件"""
        row, col = self.get_grid_pos(pos[0], pos[1])
        if row != -1 and col != -1:
            self.clear_selection()
            self.add_cell_to_selection(row, col)

    def handle_mouse_motion(self, pos):
        """处理鼠标移动事件"""
        if self.selected_cells:  # 只有在已经开始选择时才处理
            row, col = self.get_grid_pos(pos[0], pos[1])
            if row != -1 and col != -1:
                self.add_cell_to_selection(row, col)

    def handle_mouse_up(self):
        """处理鼠标释放事件"""
        if self.selected_cells:
            self.submit_word()

    def reset_game(self):
        """重置游戏"""
        self.score = 0
        self.clear_selection()
        self.init_grid()

    def run(self):
        """运行游戏主循环"""
        running = True
        mouse_pressed = False

        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_r:
                        self.reset_game()

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # 左键
                        mouse_pressed = True
                        self.handle_mouse_down(event.pos)

                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # 左键
                        mouse_pressed = False
                        self.handle_mouse_up()

                elif event.type == pygame.MOUSEMOTION:
                    if mouse_pressed:
                        self.handle_mouse_motion(event.pos)

            # 绘制游戏
            self.screen.fill(GRAY)
            self.draw_grid()
            self.draw_selection_lines()
            self.draw_ui()

            pygame.display.flip()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()

def main():
    """主函数"""
    game = WordPuzzleGame()
    game.run()

if __name__ == "__main__":
    main()
