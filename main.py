#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中英文单词消消乐游戏启动脚本
"""

from word_match_game import WordMatchGame

def main():
    """启动游戏"""
    print("欢迎来到中英文单词消消乐！")
    print("游戏规则：")
    print("1. 点击两张卡片，如果是匹配的中英文单词对，它们会消除")
    print("2. 红色文字是中文，蓝色文字是英文")
    print("3. 消除所有卡片即可获胜")
    print("4. 游戏结束后按R键重新开始")
    print("5. 按ESC或关闭窗口退出游戏")
    print("\n正在启动游戏...")

    try:
        game = WordMatchGame()
        game.run()
    except ImportError as e:
        print(f"缺少必要的库: {e}")
        print("请安装pygame库: pip install pygame")
    except Exception as e:
        print(f"游戏启动失败: {e}")

if __name__ == "__main__":
    main()