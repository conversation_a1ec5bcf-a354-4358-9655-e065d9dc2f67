import pygame
import random
import json
import sys
from typing import Dict, List, Tuple, Optional

# 游戏配置
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GRID_SIZE = 6
CELL_SIZE = 80
CELL_MARGIN = 5
COLORS = {
    'background': (240, 240, 240),
    'grid_bg': (255, 255, 255),
    'border': (200, 200, 200),
    'selected': (100, 150, 255),
    'matched': (100, 255, 100),
    'text': (50, 50, 50),
    'chinese': (255, 100, 100),
    'english': (100, 100, 255)
}

class WordCard:
    """单词卡片类"""
    def __init__(self, word: str, translation: str, is_chinese: bool, x: int, y: int):
        self.word = word
        self.translation = translation
        self.is_chinese = is_chinese
        self.x = x
        self.y = y
        self.selected = False
        self.matched = False
        self.rect = pygame.Rect(x * (CELL_SIZE + CELL_MARGIN), 
                               y * (CELL_SIZE + CELL_MARGIN), 
                               CELL_SIZE, CELL_SIZE)

    def draw(self, screen, font):
        """绘制卡片"""
        color = COLORS['grid_bg']
        if self.matched:
            color = COLORS['matched']
        elif self.selected:
            color = COLORS['selected']
        
        pygame.draw.rect(screen, color, self.rect)
        pygame.draw.rect(screen, COLORS['border'], self.rect, 2)
        
        # 选择文字颜色
        text_color = COLORS['chinese'] if self.is_chinese else COLORS['english']
        
        # 渲染文字
        text_surface = font.render(self.word, True, text_color)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)

class WordMatchGame:
    """单词消消乐游戏主类"""
    
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("中英文单词消消乐")
        
        # 字体设置
        self.font = pygame.font.Font(None, 24)
        self.title_font = pygame.font.Font(None, 36)
        
        # 游戏状态
        self.grid = [[None for _ in range(GRID_SIZE)] for _ in range(GRID_SIZE)]
        self.selected_cards = []
        self.score = 0
        self.matches_found = 0
        self.game_over = False
        
        # 加载单词本
        self.word_pairs = self.load_word_pairs()
        
        # 初始化游戏
        self.init_game()
    
    def load_word_pairs(self) -> List[Tuple[str, str]]:
        """加载单词本，返回中英文对照列表"""
        # 默认单词本
        default_words = [
            ("苹果", "apple"), ("香蕉", "banana"), ("橙子", "orange"),
            ("猫", "cat"), ("狗", "dog"), ("鸟", "bird"),
            ("红色", "red"), ("蓝色", "blue"), ("绿色", "green"),
            ("大", "big"), ("小", "small"), ("快", "fast"),
            ("书", "book"), ("笔", "pen"), ("桌子", "table"),
            ("水", "water"), ("火", "fire"), ("风", "wind"),
            ("太阳", "sun"), ("月亮", "moon"), ("星星", "star"),
            ("房子", "house"), ("车", "car"), ("飞机", "plane")
        ]
        
        try:
            # 尝试从文件加载单词本
            with open('words.json', 'r', encoding='utf-8') as f:
                loaded_words = json.load(f)
                return [(pair['chinese'], pair['english']) for pair in loaded_words]
        except FileNotFoundError:
            print("未找到words.json文件，使用默认单词本")
            return default_words
    
    def init_game(self):
        """初始化游戏网格"""
        # 选择足够的单词对来填充网格
        pairs_needed = (GRID_SIZE * GRID_SIZE) // 2
        selected_pairs = random.sample(self.word_pairs, min(pairs_needed, len(self.word_pairs)))
        
        # 创建卡片列表
        cards = []
        for chinese, english in selected_pairs:
            cards.append(WordCard(chinese, english, True, 0, 0))
            cards.append(WordCard(english, chinese, False, 0, 0))
        
        # 随机打乱卡片
        random.shuffle(cards)
        
        # 将卡片放置到网格中
        card_index = 0
        for y in range(GRID_SIZE):
            for x in range(GRID_SIZE):
                if card_index < len(cards):
                    card = cards[card_index]
                    card.x = x
                    card.y = y
                    card.rect = pygame.Rect(x * (CELL_SIZE + CELL_MARGIN) + 50, 
                                          y * (CELL_SIZE + CELL_MARGIN) + 100, 
                                          CELL_SIZE, CELL_SIZE)
                    self.grid[y][x] = card
                    card_index += 1
    
    def handle_click(self, pos):
        """处理鼠标点击"""
        if self.game_over:
            return
        
        # 检查点击的是哪个卡片
        for row in self.grid:
            for card in row:
                if card and card.rect.collidepoint(pos) and not card.matched:
                    self.select_card(card)
                    break
    
    def select_card(self, card):
        """选择卡片"""
        if card in self.selected_cards:
            # 取消选择
            card.selected = False
            self.selected_cards.remove(card)
        elif len(self.selected_cards) < 2:
            # 选择卡片
            card.selected = True
            self.selected_cards.append(card)
            
            # 检查是否选择了两张卡片
            if len(self.selected_cards) == 2:
                self.check_match()
    
    def check_match(self):
        """检查两张卡片是否匹配"""
        if len(self.selected_cards) != 2:
            return
        
        card1, card2 = self.selected_cards
        
        # 检查是否为匹配的中英文对
        is_match = ((card1.is_chinese and not card2.is_chinese and card1.translation == card2.word) or
                   (not card1.is_chinese and card2.is_chinese and card1.word == card2.translation))
        
        if is_match:
            # 匹配成功
            card1.matched = True
            card2.matched = True
            card1.selected = False
            card2.selected = False
            self.score += 10
            self.matches_found += 1
            
            # 检查游戏是否结束
            if self.matches_found >= (GRID_SIZE * GRID_SIZE) // 2:
                self.game_over = True
        else:
            # 匹配失败，延迟取消选择
            pygame.time.set_timer(pygame.USEREVENT + 1, 1000)
        
        self.selected_cards.clear()
    
    def clear_selection(self):
        """清除选择"""
        for card in self.selected_cards:
            card.selected = False
        self.selected_cards.clear()
    
    def draw(self):
        """绘制游戏界面"""
        self.screen.fill(COLORS['background'])
        
        # 绘制标题
        title_text = self.title_font.render("中英文单词消消乐", True, COLORS['text'])
        title_rect = title_text.get_rect(center=(WINDOW_WIDTH // 2, 30))
        self.screen.blit(title_text, title_rect)
        
        # 绘制分数
        score_text = self.font.render(f"分数: {self.score}", True, COLORS['text'])
        self.screen.blit(score_text, (50, 60))
        
        # 绘制匹配数
        matches_text = self.font.render(f"匹配: {self.matches_found}/{(GRID_SIZE * GRID_SIZE) // 2}", True, COLORS['text'])
        self.screen.blit(matches_text, (200, 60))
        
        # 绘制网格
        for row in self.grid:
            for card in row:
                if card:
                    card.draw(self.screen, self.font)
        
        # 绘制游戏结束信息
        if self.game_over:
            game_over_text = self.title_font.render("游戏完成！按R重新开始", True, COLORS['text'])
            game_over_rect = game_over_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT - 50))
            self.screen.blit(game_over_text, game_over_rect)
        
        pygame.display.flip()
    
    def restart_game(self):
        """重新开始游戏"""
        self.grid = [[None for _ in range(GRID_SIZE)] for _ in range(GRID_SIZE)]
        self.selected_cards = []
        self.score = 0
        self.matches_found = 0
        self.game_over = False
        self.init_game()
    
    def run(self):
        """运行游戏主循环"""
        clock = pygame.time.Clock()
        running = True
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    self.handle_click(event.pos)
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r and self.game_over:
                        self.restart_game()
                elif event.type == pygame.USEREVENT + 1:
                    # 延迟清除选择
                    self.clear_selection()
                    pygame.time.set_timer(pygame.USEREVENT + 1, 0)
            
            self.draw()
            clock.tick(60)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = WordMatchGame()
    game.run()
